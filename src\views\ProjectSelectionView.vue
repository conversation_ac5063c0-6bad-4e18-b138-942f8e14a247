<template>
  <div class="project-selection-container">
    <div class="header-section">
      <h1 class="main-title">项目名片中心</h1>
      <p class="subtitle">请选择要查看的项目</p>
    </div>

    <div class="project-grid">
      <div class="project-card" @click="navigateTo('/card/ai-sz-agent')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg" alt="AI数智代言人" class="project-icon">
          </div>
          <div class="text-content">
            <h2>AI数智代言人</h2>
            <p>AI数智代言人 创新服务</p>
          </div>
        </div>
      </div>

      <div class="project-card" @click="navigateTo('/card/GesiXiehui')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GeSiXieHui/%E5%8D%8F%E4%BC%9ALOGO.png" alt="个私协会AI名片" class="project-icon">
          </div>
          <div class="text-content">
            <h2>个私协会AI名片</h2>
            <p>上饶市民营（个私）经济协会</p>
          </div>
        </div>
      </div>


      <div class="project-card" @click="navigateTo('/card/lintian-tech')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg" alt="霖天科技AI名片" class="project-icon">
          </div>
          <div class="text-content">
            <h2>霖天科技AI名片</h2>
            <p>技术驱动价值，数据赋能未来</p>
          </div>
        </div>
      </div>

      <div class="project-card" @click="navigateTo('/card/WanWangKeJi')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png" alt="江西万网科技AI名片" class="project-icon">
          </div>
          <div class="text-content">
            <h2>江西万网科技AI名片</h2>
            <p>江西万网科技有限公司</p>
          </div>
        </div>
      </div>
      <div class="project-card" @click="navigateTo('/card/starmap-ai')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/szr.png" alt="星图AI名片" class="project-icon">
          </div>
          <div class="text-content">
            <h2>星图AI名片</h2>
            <p>霖天科技AI智能解决方案</p>
          </div>
        </div>
      </div>





      <!-- 未来可以添加更多项目卡片 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (route: string) => {
  router.push(route)
}
</script>

<style scoped>
.project-selection-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
}

.header-section {
  text-align: center;
  margin-bottom: 2rem;
}

.main-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1b4283;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.1rem;
  color: #5a6a85;
  max-width: 600px;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  width: 100%;
  max-width: 1200px;
}

.project-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(27, 66, 131, 0.15);
}

.card-content {
  display: flex;
  padding: 1.5rem;
  align-items: center;
}

.icon-wrapper {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-icon {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.text-content {
  flex-grow: 1;
}

.text-content h2 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.text-content p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
  }

  .main-title {
    font-size: 1.75rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .card-content {
    padding: 1.25rem;
  }

  .icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .text-content h2 {
    font-size: 1.1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .project-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .project-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>